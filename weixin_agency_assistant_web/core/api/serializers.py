from rest_framework import serializers

from core.models import Product


class ProductSerializer(serializers.ModelSerializer[Product]):
    class Meta:
        model = Product
        fields = [
            'product_data',
            'product_title',
            'product_price',
            'url'
        ]
        read_only_fields = [
            'product_title',
            'product_price',
        ]

        extra_kwargs = {
            'url': {'view_name': 'api:product-detail', 'lookup_field': 'pk'}
        }
