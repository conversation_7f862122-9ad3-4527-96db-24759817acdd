from rest_framework import status
from rest_framework.decorators import action
from rest_framework.mixins import ListModelMixin
from rest_framework.mixins import RetrieveModelMixin
from rest_framework.mixins import UpdateModelMixin
from rest_framework.response import Response
from rest_framework.viewsets import GenericViewSet, ModelViewSet
from django.db.models.fields.json import KeyTextTransform

from core.models import Product

from .serializers import ProductSerializer


class ProductViewSet(ModelViewSet):
    serializer_class = ProductSerializer
    queryset = Product.objects.all()
    lookup_field = "pk"

    def create(self, request, *args, **kwargs):
        """
        创建商品，如果相同 product_title 已存在则更新
        """
        # 从 product_data 中提取 title
        product_data = request.data.get('product_data', {})
        product_info = product_data.get('productInfo', {})
        title = product_info.get('title')

        if not title:
            # 如果没有 title，使用默认的创建逻辑
            return super().create(request, *args, **kwargs)

        # 查找是否已存在相同 title 的商品
        try:
            existing_product = Product.objects.get(
                product_data__productInfo__title=title
            )
            # 如果存在，更新该商品
            serializer = self.get_serializer(existing_product, data=request.data, partial=False)
            serializer.is_valid(raise_exception=True)
            self.perform_update(serializer)

            return Response(serializer.data, status=status.HTTP_200_OK)

        except Product.DoesNotExist:
            # 如果不存在，创建新商品
            return super().create(request, *args, **kwargs)
