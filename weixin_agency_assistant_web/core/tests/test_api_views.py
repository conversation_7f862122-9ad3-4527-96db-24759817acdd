from django.test import TestCase
from rest_framework.test import APIClient
from rest_framework import status
from django.urls import reverse

from core.models import Product


class TestProductViewSet(TestCase):

    def setUp(self):
        self.client = APIClient()
        self.url = reverse('api:product-list')

    def test_create_new_product(self):
        """测试创建新商品"""
        data = {
            'product_data': {
                'productInfo': {
                    'title': '测试商品1',
                    'price': 9999  # 价格以分为单位
                }
            }
        }

        response = self.client.post(self.url, data, format='json')

        assert response.status_code == status.HTTP_201_CREATED
        assert Product.objects.count() == 1

        product = Product.objects.first()
        assert product.product_title == '测试商品1'
        assert product.product_price == 99.99  # 应该转换为元

    def test_create_or_update_existing_product(self):
        """测试创建已存在的商品时进行更新"""
        # 先创建一个商品
        initial_data = {
            'product_data': {
                'productInfo': {
                    'title': '测试商品2',
                    'price': 5000,
                    'description': '初始描述'
                }
            }
        }

        response = self.client.post(self.url, initial_data, format='json')
        assert response.status_code == status.HTTP_201_CREATED
        assert Product.objects.count() == 1

        # 再次创建相同 title 的商品，应该更新而不是创建新的
        updated_data = {
            'product_data': {
                'productInfo': {
                    'title': '测试商品2',  # 相同的 title
                    'price': 8000,  # 不同的价格
                    'description': '更新后的描述'
                }
            }
        }

        response = self.client.post(self.url, updated_data, format='json')
        assert response.status_code == status.HTTP_200_OK  # 更新返回 200
        assert Product.objects.count() == 1  # 仍然只有一个商品

        product = Product.objects.first()
        assert product.product_title == '测试商品2'
        assert product.product_price == 80.00  # 价格应该更新
        assert product.product_data['productInfo']['description'] == '更新后的描述'

    def test_create_without_title(self):
        """测试创建没有 title 的商品"""
        data = {
            'product_data': {
                'productInfo': {
                    'price': 1000
                    # 没有 title
                }
            }
        }

        response = self.client.post(self.url, data, format='json')

        # 应该正常创建，但 product_title 会是 None 或空
        assert response.status_code == status.HTTP_201_CREATED
        assert Product.objects.count() == 1

    def test_create_multiple_different_products(self):
        """测试创建多个不同的商品"""
        products_data = [
            {
                'product_data': {
                    'productInfo': {
                        'title': '商品A',
                        'price': 1000
                    }
                }
            },
            {
                'product_data': {
                    'productInfo': {
                        'title': '商品B',
                        'price': 2000
                    }
                }
            }
        ]

        for data in products_data:
            response = self.client.post(self.url, data, format='json')
            assert response.status_code == status.HTTP_201_CREATED

        assert Product.objects.count() == 2

        titles = list(Product.objects.values_list('product_title', flat=True))
        assert '商品A' in titles
        assert '商品B' in titles
